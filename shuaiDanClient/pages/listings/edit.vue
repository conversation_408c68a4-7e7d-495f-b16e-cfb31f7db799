<template>
  <view class="edit-container">
    <!-- 加载状态 -->
    <view v-if="isLoading" class="loading-container">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 编辑表单 -->
    <scroll-view v-else class="form-scroll" scroll-y>
      <form @submit="handleSubmit">
        <!-- 基本信息 -->
        <view class="form-section">
          <view class="section-title">基本信息</view>
          
          <view class="form-item">
            <text class="label required">信息类型</text>
            <picker 
              :value="selectedTypeIndex" 
              :range="listingTypes" 
              @change="onTypeChange"
              class="form-picker"
            >
              <view class="picker-content">
                <text>{{ listingTypes[selectedTypeIndex] || '请选择' }}</text>
                <text class="picker-arrow">></text>
              </view>
            </picker>
          </view>

          <view class="form-item">
            <text class="label required">公司名称</text>
            <input 
              v-model="formData.company_name"
              placeholder="请输入公司/个体户名称"
              class="form-input"
              maxlength="100"
            />
          </view>

          <view class="form-item">
            <text class="label">注册省份</text>
            <picker 
              :value="selectedProvinceIndex" 
              :range="provinces" 
              @change="onProvinceChange"
              class="form-picker"
            >
              <view class="picker-content">
                <text>{{ provinces[selectedProvinceIndex] || '请选择省份' }}</text>
                <text class="picker-arrow">></text>
              </view>
            </picker>
          </view>

          <view class="form-item">
            <text class="label">注册城市</text>
            <picker 
              :value="selectedCityIndex" 
              :range="cities" 
              @change="onCityChange"
              class="form-picker"
            >
              <view class="picker-content">
                <text>{{ cities[selectedCityIndex] || '请选择城市' }}</text>
                <text class="picker-arrow">></text>
              </view>
            </picker>
          </view>

          <view class="form-item">
            <text class="label">成立时间</text>
            <picker 
              mode="date" 
              :value="formData.establishment_date"
              @change="onDateChange"
              class="form-picker"
            >
              <view class="picker-content">
                <text>{{ formData.establishment_date || '请选择日期' }}</text>
                <text class="picker-arrow">></text>
              </view>
            </picker>
          </view>
        </view>

        <!-- 财务信息 -->
        <view class="form-section">
          <view class="section-title">财务信息</view>
          
          <view class="form-item">
            <text class="label">转让价格</text>
            <input 
              v-model.number="formData.price"
              placeholder="请输入价格（万元）"
              type="digit"
              class="form-input"
            />
          </view>

          <view class="form-item">
            <view class="checkbox-item">
              <checkbox 
                :checked="formData.is_negotiable"
                @change="onNegotiableChange"
              />
              <text class="checkbox-label">价格可协商</text>
            </view>
          </view>

          <view class="form-item">
            <text class="label">注册资本</text>
            <picker 
              :value="selectedCapitalIndex" 
              :range="capitalRanges" 
              @change="onCapitalChange"
              class="form-picker"
            >
              <view class="picker-content">
                <text>{{ capitalRanges[selectedCapitalIndex] || '请选择注册资本范围' }}</text>
                <text class="picker-arrow">></text>
              </view>
            </picker>
          </view>

          <view class="form-item">
            <text class="label">实缴状态</text>
            <picker 
              :value="selectedPaidIndex" 
              :range="paidStatuses" 
              @change="onPaidChange"
              class="form-picker"
            >
              <view class="picker-content">
                <text>{{ paidStatuses[selectedPaidIndex] || '请选择实缴状态' }}</text>
                <text class="picker-arrow">></text>
              </view>
            </picker>
          </view>
        </view>

        <!-- 公司信息 -->
        <view class="form-section">
          <view class="section-title">公司信息</view>
          
          <view class="form-item">
            <text class="label">公司类型</text>
            <picker 
              :value="selectedCompanyTypeIndex" 
              :range="companyTypes" 
              @change="onCompanyTypeChange"
              class="form-picker"
            >
              <view class="picker-content">
                <text>{{ companyTypes[selectedCompanyTypeIndex] || '请选择公司类型' }}</text>
                <text class="picker-arrow">></text>
              </view>
            </picker>
          </view>

          <view class="form-item">
            <text class="label">税务状态</text>
            <picker 
              :value="selectedTaxIndex" 
              :range="taxStatuses" 
              @change="onTaxChange"
              class="form-picker"
            >
              <view class="picker-content">
                <text>{{ taxStatuses[selectedTaxIndex] || '请选择税务状态' }}</text>
                <text class="picker-arrow">></text>
              </view>
            </picker>
          </view>

          <view class="form-item">
            <text class="label">银行开户</text>
            <picker 
              :value="selectedBankIndex" 
              :range="bankStatuses" 
              @change="onBankChange"
              class="form-picker"
            >
              <view class="picker-content">
                <text>{{ bankStatuses[selectedBankIndex] || '请选择银行开户状态' }}</text>
                <text class="picker-arrow">></text>
              </view>
            </picker>
          </view>

          <view class="form-item">
            <text class="label">股东背景</text>
            <picker 
              :value="selectedShareholderIndex" 
              :range="shareholderBackgrounds" 
              @change="onShareholderChange"
              class="form-picker"
            >
              <view class="picker-content">
                <text>{{ shareholderBackgrounds[selectedShareholderIndex] || '请选择股东背景' }}</text>
                <text class="picker-arrow">></text>
              </view>
            </picker>
          </view>
        </view>

        <!-- 特殊资质 -->
        <view class="form-section">
          <view class="section-title">特殊资质</view>

          <view class="checkbox-group">
            <label class="checkbox-item">
              <checkbox
                :checked="formData.has_trademark"
                @click="toggleAsset('has_trademark')"
                color="#22c55e"
              />
              <text class="checkbox-label">商标</text>
            </label>

            <label class="checkbox-item">
              <checkbox
                :checked="formData.has_patent"
                @click="toggleAsset('has_patent')"
                color="#22c55e"
              />
              <text class="checkbox-label">专利</text>
            </label>

            <label class="checkbox-item">
              <checkbox
                :checked="formData.has_software_copyright"
                @click="toggleAsset('has_software_copyright')"
                color="#22c55e"
              />
              <text class="checkbox-label">软件著作权</text>
            </label>

            <label class="checkbox-item">
              <checkbox
                :checked="formData.has_license_plate"
                @click="toggleAsset('has_license_plate')"
                color="#22c55e"
              />
              <text class="checkbox-label">车牌</text>
            </label>

            <label class="checkbox-item">
              <checkbox
                :checked="formData.has_social_security"
                @click="toggleAsset('has_social_security')"
                color="#22c55e"
              />
              <text class="checkbox-label">社保</text>
            </label>

            <label class="checkbox-item">
              <checkbox
                :checked="formData.has_bidding_history"
                @click="toggleAsset('has_bidding_history')"
                color="#22c55e"
              />
              <text class="checkbox-label">投标记录</text>
            </label>
          </view>
        </view>

        <!-- 详细描述 -->
        <view class="form-section">
          <view class="section-title">详细描述</view>
          
          <view class="form-item">
            <textarea 
              v-model="formData.description"
              placeholder="请详细描述公司情况、转让原因等（选填）"
              class="form-textarea"
              maxlength="500"
            />
            <text class="char-count">{{ formData.description.length }}/500</text>
          </view>
        </view>
      </form>
    </scroll-view>

    <!-- 底部按钮 -->
    <view class="bottom-actions">
      <button 
        class="submit-btn"
        :disabled="isSubmitting"
        @click="handleSubmit"
      >
        {{ isSubmitting ? '保存中...' : '保存修改' }}
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { listingAPI, userAPI, utils } from '@/utils/api.js'

// 响应式数据
const isLoading = ref(true)
const isSubmitting = ref(false)
const listingId = ref(null)

// 表单数据
const formData = reactive({
  listing_type: '',
  company_name: '',
  registration_province: '',
  registration_city: '',
  establishment_date: '',
  price: null,
  is_negotiable: false,
  registered_capital_range: '',
  paid_in_status: '',
  company_type: '',
  tax_status: '',
  bank_account_status: '',
  shareholder_background: '',
  has_trademark: false,
  has_patent: false,
  has_software_copyright: false,
  has_license_plate: false,
  has_social_security: false,
  has_bidding_history: false,
  description: ''
})

// 选择器数据
const listingTypes = ['公司', '个体户', '代账户']
const selectedTypeIndex = ref(-1)

const provinces = ['北京', '上海', '广东', '江苏', '浙江', '山东', '河南', '四川', '湖北', '湖南']
const selectedProvinceIndex = ref(-1)

const cities = ['请先选择省份']
const selectedCityIndex = ref(-1)

const capitalRanges = ['10万以下', '10-50万', '50-100万', '100-500万', '500-1000万', '1000万以上']
const selectedCapitalIndex = ref(-1)

const paidStatuses = ['已实缴', '未实缴', '不确定']
const selectedPaidIndex = ref(-1)

const companyTypes = ['普通公司', '国家局公司', '上市公司', '不确定']
const selectedCompanyTypeIndex = ref(-1)

const taxStatuses = ['未登记', '小规模', '一般纳税人', '未开业', '不确定']
const selectedTaxIndex = ref(-1)

const bankStatuses = ['已开户', '未开户', '不确定']
const selectedBankIndex = ref(-1)

const shareholderBackgrounds = ['自然人', '国央企', '外资', '不确定']
const selectedShareholderIndex = ref(-1)

// 生命周期
onLoad((options) => {
  const { id } = options
  if (id) {
    listingId.value = id
    loadListingData(id)
  } else {
    isLoading.value = false
    uni.showToast({
      title: '缺少信息ID',
      icon: 'error'
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
})

// 方法定义
const loadListingData = async (id) => {
  try {
    isLoading.value = true

    const response = await listingAPI.getListingDetail(id)

    if (response.success) {
      const data = response.data

      // 填充表单数据
      formData.listing_type = data.listing_type
      formData.company_name = data.company_name
      formData.registration_province = data.registration_province || ''
      formData.registration_city = data.registration_city || ''
      formData.establishment_date = data.establishment_date || ''
      formData.price = data.price
      formData.is_negotiable = data.is_negotiable
      formData.registered_capital_range = data.registered_capital_range || ''
      formData.paid_in_status = data.paid_in_status || ''
      formData.company_type = data.company_type || ''
      formData.tax_status = data.tax_status || ''
      formData.bank_account_status = data.bank_account_status || ''
      formData.shareholder_background = data.shareholder_background || ''
      formData.has_trademark = data.has_trademark
      formData.has_patent = data.has_patent
      formData.has_software_copyright = data.has_software_copyright
      formData.has_license_plate = data.has_license_plate
      formData.has_social_security = data.has_social_security
      formData.has_bidding_history = data.has_bidding_history
      formData.description = data.description || ''

      // 设置选择器索引
      selectedTypeIndex.value = listingTypes.indexOf(data.listing_type)
      selectedProvinceIndex.value = provinces.indexOf(data.registration_province)
      selectedCityIndex.value = cities.indexOf(data.registration_city)
      selectedCapitalIndex.value = capitalRanges.indexOf(data.registered_capital_range)
      selectedPaidIndex.value = paidStatuses.indexOf(data.paid_in_status)
      selectedCompanyTypeIndex.value = companyTypes.indexOf(data.company_type)
      selectedTaxIndex.value = taxStatuses.indexOf(data.tax_status)
      selectedBankIndex.value = bankStatuses.indexOf(data.bank_account_status)
      selectedShareholderIndex.value = shareholderBackgrounds.indexOf(data.shareholder_background)

    } else {
      utils.handleError(new Error(response.message), '获取信息失败')
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }

  } catch (error) {
    utils.handleError(error, '获取信息失败')
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  } finally {
    isLoading.value = false
  }
}

const onTypeChange = (e) => {
  selectedTypeIndex.value = e.detail.value
  formData.listing_type = listingTypes[e.detail.value]
}

const onProvinceChange = (e) => {
  selectedProvinceIndex.value = e.detail.value
  formData.registration_province = provinces[e.detail.value]

  // 重置城市选择
  selectedCityIndex.value = -1
  formData.registration_city = ''

  // 这里可以根据省份更新城市列表
  // 为简化，暂时使用固定城市列表
}

const onCityChange = (e) => {
  selectedCityIndex.value = e.detail.value
  formData.registration_city = cities[e.detail.value]
}

const onDateChange = (e) => {
  formData.establishment_date = e.detail.value
}

const onNegotiableChange = (e) => {
  formData.is_negotiable = e.detail.value
}

const onCapitalChange = (e) => {
  selectedCapitalIndex.value = e.detail.value
  formData.registered_capital_range = capitalRanges[e.detail.value]
}

const onPaidChange = (e) => {
  selectedPaidIndex.value = e.detail.value
  formData.paid_in_status = paidStatuses[e.detail.value]
}

const onCompanyTypeChange = (e) => {
  selectedCompanyTypeIndex.value = e.detail.value
  formData.company_type = companyTypes[e.detail.value]
}

const onTaxChange = (e) => {
  selectedTaxIndex.value = e.detail.value
  formData.tax_status = taxStatuses[e.detail.value]
}

const onBankChange = (e) => {
  selectedBankIndex.value = e.detail.value
  formData.bank_account_status = bankStatuses[e.detail.value]
}

const onShareholderChange = (e) => {
  selectedShareholderIndex.value = e.detail.value
  formData.shareholder_background = shareholderBackgrounds[e.detail.value]
}

// 切换资产状态
const toggleAsset = (field) => {
  formData[field] = !formData[field]
}

const handleSubmit = async () => {
  if (isSubmitting.value) return

  try {
    // 基本验证
    if (!formData.listing_type) {
      uni.showToast({
        title: '请选择信息类型',
        icon: 'error'
      })
      return
    }

    if (!formData.company_name.trim()) {
      uni.showToast({
        title: '请输入公司名称',
        icon: 'error'
      })
      return
    }

    isSubmitting.value = true

    // 准备提交数据
    const submitData = { ...formData }

    // 清理空值
    Object.keys(submitData).forEach(key => {
      if (submitData[key] === '' || submitData[key] === null) {
        delete submitData[key]
      }
    })

    const response = await listingAPI.updateListing(listingId.value, submitData)

    if (response.success) {
      uni.showToast({
        title: '修改成功',
        icon: 'success'
      })

      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    } else {
      utils.handleError(new Error(response.message), '修改失败')
    }

  } catch (error) {
    utils.handleError(error, '修改失败')
  } finally {
    isSubmitting.value = false
  }
}
</script>

<style scoped>
.edit-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.loading-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

.form-scroll {
  flex: 1;
  padding: 20rpx;
}

.form-section {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.label.required::after {
  content: '*';
  color: #ef4444;
  margin-left: 5rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: white;
}

.form-picker {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  background: white;
}

.picker-content {
  height: 100%;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 28rpx;
  color: #333;
}

.picker-arrow {
  color: #999;
}

.form-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: white;
}

.char-count {
  display: block;
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

.checkbox-group {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 10rpx;
  font-size: 28rpx;
  padding: 20rpx;
  border: 1rpx solid #e5e5e5;
  border-radius: 8rpx;
  background: #f9f9f9;
}

.checkbox-label {
  color: #333;
}

.bottom-actions {
  padding: 20rpx;
  background: white;
  border-top: 1rpx solid #eee;
}

.submit-btn {
  width: 100%;
  height: 90rpx;
  background: #22c55e;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.submit-btn:disabled {
  background: #ccc;
}
</style>
